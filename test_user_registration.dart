import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'lib/models/user.dart' as model_user;
import 'lib/services/user_service.dart';

/// 测试用户注册功能的脚本
/// 用于验证数据库架构修复是否成功
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // 初始化Supabase（需要替换为实际的配置）
  await Supabase.initialize(
    url: 'YOUR_SUPABASE_URL',
    anonKey: 'YOUR_SUPABASE_ANON_KEY',
  );
  
  final userService = UserService();
  
  print('==============================================');
  print('开始测试用户注册功能...');
  print('==============================================');
  
  // 测试1: 创建测试用户
  await testCreateUser(userService);
  
  // 测试2: 测试用户数据读取
  await testReadUser(userService);
  
  // 测试3: 测试用户数据更新
  await testUpdateUser(userService);
  
  print('==============================================');
  print('所有测试完成！');
  print('==============================================');
}

/// 测试创建用户
Future<void> testCreateUser(UserService userService) async {
  print('\n📝 测试1: 创建用户');
  
  try {
    final testUser = model_user.User(
      id: 'test-user-${DateTime.now().millisecondsSinceEpoch}',
      username: 'test_user_${DateTime.now().millisecondsSinceEpoch}',
      nickname: '测试用户',
      email: '<EMAIL>',
      phoneNumber: '13800138000',
      bio: '这是一个测试用户',
      following: ['user1', 'user2'],
      followers: ['user3'],
      publishedSpots: ['spot1'],
      favoriteSpots: ['spot2', 'spot3'],
    );
    
    await userService.createUserInDatabase(testUser);
    print('✅ 用户创建成功: ${testUser.username}');
    
    // 设置为当前用户以便后续测试
    await userService.setCurrentUser(testUser);
    print('✅ 设置当前用户成功');
    
  } catch (e) {
    print('❌ 用户创建失败: $e');
    if (e.toString().contains('favorite_spots')) {
      print('💡 这表明数据库架构修复可能未生效');
      print('💡 请确保已执行 fix_user_table_schema.sql 迁移脚本');
    }
  }
}

/// 测试读取用户
Future<void> testReadUser(UserService userService) async {
  print('\n📖 测试2: 读取用户数据');
  
  try {
    final currentUser = userService.currentUser;
    if (currentUser != null) {
      print('✅ 成功读取当前用户: ${currentUser.username}');
      print('   - 关注数: ${currentUser.following.length}');
      print('   - 粉丝数: ${currentUser.followers.length}');
      print('   - 发布钓点数: ${currentUser.publishedSpots.length}');
      print('   - 收藏钓点数: ${currentUser.favoriteSpots.length}');
    } else {
      print('❌ 无法读取当前用户');
    }
    
    // 测试从数据库获取用户
    final users = await userService.getAllUsers();
    print('✅ 成功获取用户列表，共 ${users.length} 个用户');
    
  } catch (e) {
    print('❌ 读取用户失败: $e');
  }
}

/// 测试更新用户
Future<void> testUpdateUser(UserService userService) async {
  print('\n✏️ 测试3: 更新用户数据');
  
  try {
    final currentUser = userService.currentUser;
    if (currentUser != null) {
      // 修改用户数据
      currentUser.bio = '更新后的个人简介';
      currentUser.favoriteSpots.add('new_spot');
      currentUser.following.add('new_user');
      
      await userService.updateUser(currentUser);
      print('✅ 用户更新成功');
      
      // 验证更新是否生效
      final updatedUser = await userService.getUserById(currentUser.id);
      if (updatedUser != null) {
        print('✅ 验证更新成功:');
        print('   - 新简介: ${updatedUser.bio}');
        print('   - 收藏钓点数: ${updatedUser.favoriteSpots.length}');
        print('   - 关注用户数: ${updatedUser.following.length}');
      }
    } else {
      print('❌ 没有当前用户可供更新');
    }
    
  } catch (e) {
    print('❌ 更新用户失败: $e');
  }
}

/// 测试收藏功能
Future<void> testFavoriteSpot(UserService userService) async {
  print('\n⭐ 测试4: 收藏钓点功能');
  
  try {
    final currentUser = userService.currentUser;
    if (currentUser != null) {
      const testSpotId = 'test-spot-123';
      
      // 添加收藏
      await userService.addFavoriteSpot(currentUser.id, testSpotId);
      print('✅ 添加收藏成功');
      
      // 验证收藏是否添加
      final updatedUser = await userService.getUserById(currentUser.id);
      if (updatedUser != null && updatedUser.favoriteSpots.contains(testSpotId)) {
        print('✅ 收藏验证成功');
      } else {
        print('❌ 收藏验证失败');
      }
      
      // 移除收藏
      await userService.removeFavoriteSpot(currentUser.id, testSpotId);
      print('✅ 移除收藏成功');
      
    } else {
      print('❌ 没有当前用户可供测试');
    }
    
  } catch (e) {
    print('❌ 收藏功能测试失败: $e');
  }
}

/// 测试关注功能
Future<void> testFollowUser(UserService userService) async {
  print('\n👥 测试5: 关注用户功能');
  
  try {
    final currentUser = userService.currentUser;
    if (currentUser != null) {
      const testTargetUserId = 'test-target-user';
      
      // 创建目标用户（用于测试）
      final targetUser = model_user.User(
        id: testTargetUserId,
        username: 'target_user',
        nickname: '目标用户',
        email: '<EMAIL>',
      );
      
      await userService.createUserInDatabase(targetUser);
      print('✅ 创建目标用户成功');
      
      // 关注用户
      await userService.followUser(currentUser.id, testTargetUserId);
      print('✅ 关注用户成功');
      
      // 验证关注关系
      final updatedCurrentUser = await userService.getUserById(currentUser.id);
      final updatedTargetUser = await userService.getUserById(testTargetUserId);
      
      if (updatedCurrentUser != null && 
          updatedCurrentUser.following.contains(testTargetUserId)) {
        print('✅ 关注关系验证成功');
      } else {
        print('❌ 关注关系验证失败');
      }
      
      if (updatedTargetUser != null && 
          updatedTargetUser.followers.contains(currentUser.id)) {
        print('✅ 粉丝关系验证成功');
      } else {
        print('❌ 粉丝关系验证失败');
      }
      
    } else {
      print('❌ 没有当前用户可供测试');
    }
    
  } catch (e) {
    print('❌ 关注功能测试失败: $e');
  }
}
