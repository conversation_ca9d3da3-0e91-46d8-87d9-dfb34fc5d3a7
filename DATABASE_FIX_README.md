# 数据库架构修复指南

## 问题描述

用户注册功能出现数据库架构不匹配错误：
```
PostgrestException: Could not find the 'favorite_spots' column of 'users' in the schema cache
```

## 问题原因

**架构不匹配**：Flutter应用代码期望在`users`表中存储数组字段（`favorite_spots`、`following`、`followers`、`published_spots`），但实际数据库表结构中缺少这些字段。

### 具体分析

1. **应用代码期望的字段**：
   - `favorite_spots` - 收藏的钓点ID列表
   - `published_spots` - 发布的钓点ID列表  
   - `following` - 关注的用户ID列表
   - `followers` - 粉丝用户ID列表

2. **实际数据库结构**：
   - 使用了规范化设计，通过独立的关系表存储这些关系
   - `spot_favorites` 表存储收藏关系
   - `user_follows` 表存储关注关系

3. **冲突点**：
   - 代码在用户注册时尝试插入数组字段到`users`表
   - 数据库中不存在这些字段，导致PostgrestException

## 修复方案

采用**混合架构**：保留关系表的同时，在`users`表中添加数组字段作为缓存，通过触发器保持数据同步。

### 优势
- ✅ 保持数据库最佳实践（规范化设计）
- ✅ 满足应用代码需求（数组字段访问）
- ✅ 自动数据同步（触发器机制）
- ✅ 向后兼容（不破坏现有功能）

## 修复步骤

### 1. 执行数据库迁移

运行修复脚本：
```sql
-- 在Supabase SQL编辑器中执行
\i supabase/migrations/fix_user_table_schema.sql
```

或者手动执行以下步骤：

#### 步骤1：添加数组字段
```sql
ALTER TABLE users ADD COLUMN following TEXT[] DEFAULT '{}';
ALTER TABLE users ADD COLUMN followers TEXT[] DEFAULT '{}';
ALTER TABLE users ADD COLUMN published_spots TEXT[] DEFAULT '{}';
ALTER TABLE users ADD COLUMN favorite_spots TEXT[] DEFAULT '{}';
```

#### 步骤2：创建同步触发器
```sql
-- 创建触发器函数（详见 fix_user_table_schema.sql）
CREATE OR REPLACE FUNCTION sync_user_favorites() RETURNS TRIGGER AS $$
-- ... 触发器代码
```

#### 步骤3：迁移现有数据
```sql
-- 同步现有数据到数组字段
SELECT migrate_existing_data();
```

### 2. 验证修复结果

#### 方法1：使用测试脚本
```bash
dart run test_user_registration.dart
```

#### 方法2：手动验证
在Supabase SQL编辑器中执行：
```sql
-- 检查字段是否存在
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'users' 
AND column_name IN ('following', 'followers', 'published_spots', 'favorite_spots');

-- 检查数据是否同步
SELECT id, username, 
       array_length(following, 1) as following_count,
       array_length(followers, 1) as followers_count,
       array_length(published_spots, 1) as published_spots_count,
       array_length(favorite_spots, 1) as favorite_spots_count
FROM users 
LIMIT 5;
```

### 3. 测试用户注册

在Flutter应用中测试用户注册功能：
```dart
// 应该不再出现 PostgrestException
final user = await userService.registerWithEmail(
  email: '<EMAIL>',
  password: 'password123',
);
```

## 修复内容详解

### 1. 数据库表结构更新

**修改前的users表**：
```sql
CREATE TABLE users (
  id UUID PRIMARY KEY,
  username TEXT NOT NULL UNIQUE,
  nickname TEXT NOT NULL,
  email TEXT NOT NULL UNIQUE,
  phone TEXT DEFAULT '',
  avatar_url TEXT DEFAULT '',
  bio TEXT DEFAULT '',
  points INTEGER DEFAULT 0,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  last_login_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);
```

**修改后的users表**：
```sql
CREATE TABLE users (
  -- 原有字段...
  following TEXT[] DEFAULT '{}',
  followers TEXT[] DEFAULT '{}',
  published_spots TEXT[] DEFAULT '{}',
  favorite_spots TEXT[] DEFAULT '{}',
  -- 其他字段...
);
```

### 2. 数据同步机制

通过触发器实现关系表与数组字段的双向同步：

- **收藏同步**：`spot_favorites` ↔ `users.favorite_spots`
- **关注同步**：`user_follows` ↔ `users.following/followers`
- **发布同步**：`fishing_spots` ↔ `users.published_spots`

### 3. 数据迁移

自动将现有关系表中的数据同步到新的数组字段中。

## 注意事项

### 1. 数据一致性
- 触发器确保关系表和数组字段始终保持同步
- 建议优先使用关系表进行复杂查询
- 数组字段主要用于快速访问和缓存

### 2. 性能考虑
- 数组字段提高了简单查询的性能
- 复杂的关系查询仍建议使用关系表
- 定期检查数据一致性

### 3. 开发建议
- 新功能开发时优先考虑使用关系表
- 数组字段主要用于满足现有代码需求
- 避免直接修改数组字段，通过关系表操作

## 故障排除

### 问题1：修复后仍然报错
**可能原因**：Supabase缓存未刷新
**解决方案**：
1. 重启Supabase项目
2. 清除浏览器缓存
3. 重新部署应用

### 问题2：数据不同步
**可能原因**：触发器未正确创建
**解决方案**：
```sql
-- 检查触发器是否存在
SELECT trigger_name, event_manipulation, event_object_table 
FROM information_schema.triggers 
WHERE trigger_name LIKE 'trigger_sync_%';
```

### 问题3：现有数据丢失
**可能原因**：迁移脚本执行失败
**解决方案**：
1. 检查关系表数据是否完整
2. 重新执行数据迁移函数
3. 手动同步特定用户数据

## 联系支持

如果遇到问题，请提供以下信息：
1. 错误日志完整内容
2. Supabase项目配置
3. 执行的SQL语句
4. 数据库表结构截图

---

**修复完成后，用户注册功能应该可以正常工作，不再出现架构不匹配错误。**
