-- =====================================================
-- 修复用户表架构不匹配问题
-- =====================================================
-- 版本: 2.1
-- 创建时间: 2025-06-25
-- 问题: PostgrestException: Could not find the 'favorite_spots' column of 'users' in the schema cache
-- 解决方案: 添加缺失的数组字段并实现数据同步

-- =====================================================
-- 1. 添加缺失的数组字段到users表
-- =====================================================

-- 检查并添加数组字段（如果不存在）
DO $$
BEGIN
  -- 添加following字段
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'users' AND column_name = 'following'
  ) THEN
    ALTER TABLE users ADD COLUMN following TEXT[] DEFAULT '{}';
    RAISE NOTICE '已添加following字段到users表';
  END IF;

  -- 添加followers字段
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'users' AND column_name = 'followers'
  ) THEN
    ALTER TABLE users ADD COLUMN followers TEXT[] DEFAULT '{}';
    RAISE NOTICE '已添加followers字段到users表';
  END IF;

  -- 添加published_spots字段
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'users' AND column_name = 'published_spots'
  ) THEN
    ALTER TABLE users ADD COLUMN published_spots TEXT[] DEFAULT '{}';
    RAISE NOTICE '已添加published_spots字段到users表';
  END IF;

  -- 添加favorite_spots字段
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'users' AND column_name = 'favorite_spots'
  ) THEN
    ALTER TABLE users ADD COLUMN favorite_spots TEXT[] DEFAULT '{}';
    RAISE NOTICE '已添加favorite_spots字段到users表';
  END IF;
END $$;

-- =====================================================
-- 2. 创建数据同步触发器函数
-- =====================================================

-- 同步用户收藏数据到users表
CREATE OR REPLACE FUNCTION sync_user_favorites()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'INSERT' THEN
    -- 添加收藏时，更新users表的favorite_spots数组
    UPDATE users 
    SET favorite_spots = array_append(favorite_spots, NEW.spot_id::text)
    WHERE id = NEW.user_id 
    AND NOT (NEW.spot_id::text = ANY(favorite_spots));
    RETURN NEW;
  ELSIF TG_OP = 'DELETE' THEN
    -- 删除收藏时，从users表的favorite_spots数组中移除
    UPDATE users 
    SET favorite_spots = array_remove(favorite_spots, OLD.spot_id::text)
    WHERE id = OLD.user_id;
    RETURN OLD;
  END IF;
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- 同步用户关注数据到users表
CREATE OR REPLACE FUNCTION sync_user_follows()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'INSERT' THEN
    -- 添加关注时，更新关注者的following数组和被关注者的followers数组
    UPDATE users 
    SET following = array_append(following, NEW.following_id::text)
    WHERE id = NEW.follower_id 
    AND NOT (NEW.following_id::text = ANY(following));
    
    UPDATE users 
    SET followers = array_append(followers, NEW.follower_id::text)
    WHERE id = NEW.following_id 
    AND NOT (NEW.follower_id::text = ANY(followers));
    
    RETURN NEW;
  ELSIF TG_OP = 'DELETE' THEN
    -- 取消关注时，从相应数组中移除
    UPDATE users 
    SET following = array_remove(following, OLD.following_id::text)
    WHERE id = OLD.follower_id;
    
    UPDATE users 
    SET followers = array_remove(followers, OLD.follower_id::text)
    WHERE id = OLD.following_id;
    
    RETURN OLD;
  END IF;
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- 同步发布钓点数据到users表
CREATE OR REPLACE FUNCTION sync_user_published_spots()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'INSERT' THEN
    -- 发布钓点时，更新users表的published_spots数组
    UPDATE users 
    SET published_spots = array_append(published_spots, NEW.id::text)
    WHERE id = NEW.user_id 
    AND NOT (NEW.id::text = ANY(published_spots));
    RETURN NEW;
  ELSIF TG_OP = 'DELETE' THEN
    -- 删除钓点时，从users表的published_spots数组中移除
    UPDATE users 
    SET published_spots = array_remove(published_spots, OLD.id::text)
    WHERE id = OLD.user_id;
    RETURN OLD;
  END IF;
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 3. 创建或更新触发器
-- =====================================================

-- 删除现有触发器（如果存在）
DROP TRIGGER IF EXISTS trigger_sync_user_favorites ON spot_favorites;
DROP TRIGGER IF EXISTS trigger_sync_user_follows ON user_follows;
DROP TRIGGER IF EXISTS trigger_sync_user_published_spots ON fishing_spots;

-- 创建新触发器
CREATE TRIGGER trigger_sync_user_favorites
  AFTER INSERT OR DELETE ON spot_favorites
  FOR EACH ROW EXECUTE FUNCTION sync_user_favorites();

CREATE TRIGGER trigger_sync_user_follows
  AFTER INSERT OR DELETE ON user_follows
  FOR EACH ROW EXECUTE FUNCTION sync_user_follows();

CREATE TRIGGER trigger_sync_user_published_spots
  AFTER INSERT OR DELETE ON fishing_spots
  FOR EACH ROW EXECUTE FUNCTION sync_user_published_spots();

-- =====================================================
-- 4. 迁移现有数据
-- =====================================================

-- 同步现有数据到users表的数组字段
CREATE OR REPLACE FUNCTION migrate_existing_data()
RETURNS void AS $$
DECLARE
  user_count INTEGER;
BEGIN
  -- 获取用户总数
  SELECT COUNT(*) INTO user_count FROM users;
  RAISE NOTICE '开始迁移 % 个用户的数据...', user_count;
  
  -- 同步收藏数据
  UPDATE users 
  SET favorite_spots = (
    SELECT COALESCE(array_agg(spot_id::text), '{}')
    FROM spot_favorites 
    WHERE user_id = users.id
  );
  
  -- 同步关注数据
  UPDATE users 
  SET following = (
    SELECT COALESCE(array_agg(following_id::text), '{}')
    FROM user_follows 
    WHERE follower_id = users.id
  );
  
  -- 同步粉丝数据
  UPDATE users 
  SET followers = (
    SELECT COALESCE(array_agg(follower_id::text), '{}')
    FROM user_follows 
    WHERE following_id = users.id
  );
  
  -- 同步发布钓点数据
  UPDATE users 
  SET published_spots = (
    SELECT COALESCE(array_agg(id::text), '{}')
    FROM fishing_spots 
    WHERE user_id = users.id
  );
  
  RAISE NOTICE '数据迁移完成：已同步现有的收藏、关注和发布数据到users表';
END;
$$ LANGUAGE plpgsql;

-- 执行数据迁移
SELECT migrate_existing_data();

-- =====================================================
-- 5. 验证修复结果
-- =====================================================

-- 验证字段是否存在
DO $$
DECLARE
  missing_fields TEXT[] := '{}';
BEGIN
  -- 检查所有必需字段
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'following') THEN
    missing_fields := array_append(missing_fields, 'following');
  END IF;
  
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'followers') THEN
    missing_fields := array_append(missing_fields, 'followers');
  END IF;
  
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'published_spots') THEN
    missing_fields := array_append(missing_fields, 'published_spots');
  END IF;
  
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'favorite_spots') THEN
    missing_fields := array_append(missing_fields, 'favorite_spots');
  END IF;
  
  IF array_length(missing_fields, 1) > 0 THEN
    RAISE EXCEPTION '修复失败：仍然缺少字段: %', array_to_string(missing_fields, ', ');
  ELSE
    RAISE NOTICE '✅ 验证通过：所有必需字段都已存在';
  END IF;
END $$;

-- 输出修复完成信息
DO $$
BEGIN
  RAISE NOTICE '==============================================';
  RAISE NOTICE '✅ 用户表架构修复完成！';
  RAISE NOTICE '修复时间: %', NOW();
  RAISE NOTICE '==============================================';
  RAISE NOTICE '修复内容:';
  RAISE NOTICE '- ✅ 添加了users表缺失的数组字段';
  RAISE NOTICE '- ✅ 创建了数据同步触发器';
  RAISE NOTICE '- ✅ 迁移了现有数据到新字段';
  RAISE NOTICE '- ✅ 验证了修复结果';
  RAISE NOTICE '==============================================';
  RAISE NOTICE '现在可以正常使用用户注册功能了！';
  RAISE NOTICE '==============================================';
END $$;
